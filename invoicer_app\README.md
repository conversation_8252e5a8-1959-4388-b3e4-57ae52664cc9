# MacInvoicer

**Automated Invoice Processing Tool with AI-Powered Data Extraction**

MacInvoicer is a comprehensive Python application that automatically monitors directories for new PDF invoice files, extracts structured data using AI, and organizes the results in monthly Excel spreadsheets. The tool provides cost-effective invoice processing with comprehensive logging, error handling, and flexible configuration options.

## 🚀 Features

### Core Functionality
- **Automated Directory Monitoring**: Continuous monitoring using watchdog for real-time processing
- **Dual PDF Text Extraction**: PyPDF2 for text-based PDFs with OCR fallback for scanned documents
- **AI-Powered Data Extraction**: OpenAI Responses API integration for structured data extraction
- **Monthly Spreadsheet Organization**: Automatic Excel file creation with running totals
- **Comprehensive Logging**: Detailed audit trails with session tracking and cost analysis

### AI & Processing
- **Hybrid Processing**: NEW! Dual extraction using both text analysis and direct PDF file upload for enhanced confidence
- **Multiple Model Support**: Optimized for cost-effective models (gpt-4.1-nano, gpt-4o-mini, etc.)
- **Cost Tracking**: Real-time token usage and cost calculation
- **Session Management**: Unique session IDs for tracking and debugging
- **Error Handling**: Robust error recovery with detailed error reporting
- **Confidence Scoring**: Advanced confidence analysis when using hybrid processing

### Data Management
- **Structured Output**: Consistent data extraction with standardized fields
- **Currency Handling**: Support for multiple currencies with EUR standardization
- **Running Totals**: Automatic calculation and maintenance of cumulative amounts
- **Processing Notes**: Detailed notes for each processed invoice with confidence scores

## 📋 Prerequisites

### System Requirements
- **Operating System**: Windows 10/11 (primary), Linux, macOS
- **Python**: 3.8 or higher
- **Tesseract OCR**: For scanned PDF processing
- **Poppler**: For PDF to image conversion

### API Requirements
- **OpenAI API Key**: For AI-powered data extraction
- Valid subscription to OpenAI services

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd macinvoicer
```

### 2. Create Virtual Environment
```bash
python -m venv .venv
# Windows
.venv\Scripts\activate
# Linux/macOS
source .venv/bin/activate
```

### 3. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 4. Install System Dependencies

#### Windows
1. **Install Tesseract OCR**:
   - Download from: https://github.com/UB-Mannheim/tesseract/wiki
   - Install to default location: `C:\Program Files\Tesseract-OCR\`

2. **Install Poppler**:
   - Download from: https://github.com/oschwartz10612/poppler-windows/releases
   - Extract to: `C:\Program Files\poppler\`
   - Add to PATH or configure in `.env` file

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install tesseract-ocr
sudo apt-get install poppler-utils
```

#### macOS
```bash
brew install tesseract
brew install poppler
```

## ⚙️ Configuration

### 1. Create Environment Configuration
Copy the example configuration and customize:
```bash
cp config/.env.example config/.env
```

### 2. Configure Environment Variables
Edit `config/.env` with your settings:

```env
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4.1-nano

# Hybrid Processing (NEW FEATURE)
USE_HYBRID_EXTRACTION=false

# Optional: Custom tool paths (Windows)
TESSERACT_CMD_PATH=C:\Program Files\Tesseract-OCR\tesseract.exe
POPPLER_PATH=C:\Program Files\poppler\bin
```

### 3. Supported Models
The application supports the following cost-effective models:

**Recommended Models:**
- `gpt-4.1-nano` - Most cost-effective for simple invoices
- `gpt-4o-mini` - Excellent balance of cost and performance
- `gpt-4.1-mini` - Good for standard invoices

**Advanced Models:**
- `gpt-4o` - For complex invoices requiring detailed analysis
- `o1-mini` - For invoices requiring reasoning capabilities
- `o3-mini` - Latest reasoning model for complex scenarios

### 4. Hybrid Processing Configuration (NEW)
Enable enhanced confidence processing by setting:
```env
USE_HYBRID_EXTRACTION=true
```

**What Hybrid Processing Does:**
- Extracts data using both text analysis AND direct PDF file upload (both via Responses API)
- Compares results from both methods for accuracy validation
- Provides enhanced confidence scoring based on agreement
- Falls back gracefully if one method fails
- Detailed comparison analysis in processing notes

**When to Use:**
- ✅ Critical invoices requiring high accuracy
- ✅ Complex layouts or scanned documents
- ✅ When visual elements are important
- ❌ High-volume processing (uses ~2x API calls)
- ❌ Simple text-based invoices (standard mode sufficient)

### 5. Directory Structure
The application automatically creates the following directories:
```
macinvoicer/
├── monitored_invoices/      # Place PDF invoices here
├── output_spreadsheets/     # Monthly Excel files generated here
├── processed_invoices_notes/ # Processing notes for each invoice
├── logs/                    # Application logs
└── config/                  # Configuration files
```

## 🚀 Usage

### Starting the Application
```bash
cd macinvoicer/src
python main.py
```

### Processing Invoices
1. **Start the application** - It will begin monitoring the `monitored_invoices/` directory
2. **Add PDF invoices** - Copy or move PDF invoice files to the monitored directory
3. **Automatic processing** - The application will:
   - Detect new files immediately
   - Extract text from PDFs
   - Send to AI for data extraction
   - Append results to monthly spreadsheets
   - Generate processing notes

### Stopping the Application
Press `Ctrl+C` to stop the monitoring gracefully.

## 📊 Output Structure

### Monthly Spreadsheets
Files are created as: `YYYY-MM_invoices.xlsx`

**Columns:**
- `Processing_DateTime` - When the invoice was processed
- `Invoice_Filename` - Original PDF filename
- `Invoice_ID` - Extracted invoice reference number
- `Vendor_Name` - Company name from invoice
- `Invoice_Date` - Invoice date (YYYY-MM-DD)
- `Due_Date` - Payment due date
- `Total_Amount_EUR` - Total amount in EUR
- `VAT_Amount_EUR` - VAT/tax amount in EUR
- `Original_Currency` - Original currency from invoice
- `Original_Total_Amount` - Amount in original currency
- `AI_Confidence_Score` - AI extraction confidence (0-1)
- `Running_Total_EUR` - Cumulative total for the month

### Processing Notes
For each invoice, a detailed note file is created:
```
invoice_name_processing_note.txt
```

Contains:
- Processing timestamp and AI provider
- Extracted data summary
- AI confidence score
- Processing notes and any issues encountered

## 🔧 Development

### Project Structure
```
macinvoicer/
├── src/
│   ├── main.py                 # Main application entry point
│   ├── config_loader.py        # Configuration management
│   ├── app_logger.py           # Logging configuration
│   ├── invoice_parser.py       # PDF text extraction
│   ├── spreadsheet_manager.py  # Excel file operations
│   └── ai_handler/
│       ├── openai_ai.py        # OpenAI integration
│       └── dummy_ai.py         # Testing/mock AI
├── tests/                      # Unit and integration tests
├── docs/                       # Additional documentation
├── config/                     # Configuration files
├── requirements.txt            # Python dependencies
└── README.md                   # This file
```

### Running Tests
```bash
cd macinvoicer
python -m pytest tests/
```

### Adding New AI Providers
1. Create new module in `ai_handler/`
2. Implement the same interface as `extract_invoice_data_openai()`
3. Update `main.py` to include the new provider option

## 💰 Cost Management

### Model Costs (approximate per 1K tokens)
- **gpt-4.1-nano**: $0.00005 input, $0.0002 output
- **gpt-4o-mini**: $0.00015 input, $0.0006 output
- **gpt-4.1-mini**: $0.0001 input, $0.0004 output
- **gpt-4o**: $0.005 input, $0.015 output

### Cost Optimization Tips
1. Use `gpt-4.1-nano` for simple invoices
2. Monitor token usage in logs
3. Process invoices in batches when possible
4. Use text truncation for very long PDFs

## 🔒 Security

### API Key Management
- Store API keys in `.env` file (never commit to version control)
- Use environment variables for production deployments
- Rotate API keys regularly

### Data Privacy
- Invoice data is processed locally except for AI API calls
- No data is stored on AI provider servers (when store=False)
- Processing notes contain no sensitive data

## 🐛 Troubleshooting

### Common Issues

#### "Tesseract not found"
- Ensure Tesseract is installed and in PATH
- Set `TESSERACT_CMD_PATH` in `.env` file
- Test with: `tesseract --version`

#### "Poppler not found"
- Install poppler-utils
- Set `POPPLER_PATH` in `.env` file
- Test with: `pdftoppm --help`

#### "OpenAI API Error"
- Check API key validity
- Verify sufficient API credits
- Check network connectivity
- Review rate limits

#### "Permission Denied"
- Ensure write permissions for output directories
- Check if files are open in other applications
- Run with appropriate user permissions

### Log Analysis
Check `logs/app.log` for detailed error information:
```bash
tail -f logs/app.log
```

## 📚 Additional Documentation

- [Installation Guide](docs/INSTALLATION.md) - Detailed installation instructions
- [Configuration Guide](docs/CONFIGURATION.md) - Advanced configuration options
- [User Guide](docs/USER_GUIDE.md) - Comprehensive usage instructions
- [Developer Guide](docs/DEVELOPER_GUIDE.md) - Development and contribution guidelines
- [API Reference](docs/API_REFERENCE.md) - Complete API documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- Check the troubleshooting section above
- Review the detailed documentation in the `docs/` directory
- Open an issue for bug reports or feature requests

## 🏷️ Version History

- **v1.0.0** - Initial release with OpenAI integration
  - Core invoice processing functionality
  - Monthly spreadsheet organization
  - Comprehensive logging and error handling
  - Support for multiple OpenAI models

---

**MacInvoicer Development Team** | Version 1.0.0 