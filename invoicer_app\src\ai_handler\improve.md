Reasoning – where the tokens go and how much is avoidable
Two separate API calls per invoice
• extract_invoice_data_openai() (text) and extract_invoice_data_with_file() (file) are both executed in the “hybrid” flow.
• Whichever call runs second repeats all static instructions and, in the file path, even re-sends the PDF as input_file plus an extra input_text snippet.
• Net effect: token (and $) cost is ~2× higher than necessary for most invoices.

Oversized / duplicated instructions
• The instructions block is ~500–600 tokens and is identical in every request.
• Nearly the same block is embedded twice in the file flow: once in instructions= and again (shorter) inside the input_text list element.
• Because the block changes “every few months at most”, it is ideal for   – a reusable Prompt ID (prompt={id:"…",version:"…"}) or   – a single developer message cached by the model.
• That immediately removes ~500 input tokens from every request.

Redundant “input_text” prefix lines
• Examples:

"Extract invoice data from the following text and return the result as a JSON object:\n\n"
This sentence is already implied by the instructions; it costs ~13 tokens per call.

Extremely generous max_output_tokens (=1000)
• A valid JSON extraction rarely exceeds 120–180 tokens.
• Reserving 1000 means the model always allocates that room in its output budget.
• Dropping to max_output_tokens: 256 (or even 192) reduces billed output-context tokens by 744/1000 ≈ 75 %.

Sending both a file and text in the file flow
• After uploading the PDF, the request still contains an input_text item that restates the task.
• Only one of those inputs is needed; deleting the text element saves another ~15 tokens.

Passing large raw PDF text blindly
• Up to 12 000 characters (~2 800 tokens) are sent even though only a few lines contain the totals or header.
• Simple pre-filters (regex for currency/amount/date lines, or an inexpensive local PDF-to-JSON parser) can cut input down by >80 %.

Metadata in the API body vs. metadata field
• Fields inside the top-level metadata do not enter the model context and therefore cost 0 tokens.
• Duplicating the same data in the user message (input_text) or inside the instructions therefore wastes tokens.

Over-specific model choice by default
• gpt-4.1 is used for every invoice.  For the majority of documents gpt-4.1-mini or gpt-4o-mini (~1/4 the price) is sufficient.
• A cascading “small-first, escalate-on-error” strategy reduces both token count and per-token price.

High-entropy UUID session IDs inside user=
• When passed as part of a user message they count as ~3-4 tokens; when placed only in metadata they are free.

Conclusion – concrete steps to cut token usage
Replace the verbose instructions with a reusable prompt template
• Create the template once in the dashboard.
• In code use:

prompt={ "id": "pmpt_invoice_extract", "variables": { } }
• Token saving: ~500 input tokens per call.

Make one API call per invoice
• Primary strategy: try the cheaper file-only flow (input_file + instructions).
• Fallback to text flow only if the first call errors or the PDF is oversized for file upload.
• Immediate 40-50 % token & cost reduction (no second call in the normal path).

Trim the input payload
• Remove the “Extract invoice data …” sentence from input_text.
• Do not include input_text at all in the file flow.
• Pre-filter PDF text to the 1–2 k relevant characters before sending.

Lower max_output_tokens to 256 (or lower once you measure real output sizes).
• Cuts reserved output context by ≈ 75 %.

Move dynamic IDs (session_id, filename) out of the user message and into metadata.
• Saves ~3–6 tokens per request.

Adopt a model cascade
• Start with gpt-4.1-mini (or gpt-4o-mini if you need vision features).
• Retry with gpt-4.1 only on low-confidence or error.
• Combines lower token price with fewer tokens (smaller models often compress instructions better).

Optional: fine-tune or function-call schema
• A small fine-tune (or function-call specification) can replace a chunk of the natural-language instructions, further shrinking input tokens by another 100–200.

Applying steps 1–5 typically yields a 60-80 % reduction in billable tokens per invoice, and combining them with the model cascade often delivers a total cost drop of 80-90 % without sacrificing accuracy.

