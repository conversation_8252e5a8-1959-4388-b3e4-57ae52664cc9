"""
OpenAI integration module for invoice data extraction.

This module provides AI-powered invoice data extraction using OpenAI's Responses API.
It handles structured data extraction from PDF text content, converts the data to
standardized formats, and provides comprehensive logging and cost tracking.

Key Features:
    - OpenAI Responses API integration for structured data extraction
    - Comprehensive cost calculation and usage tracking
    - Session-based logging for audit trails
    - Error handling for various API failure scenarios
    - Support for multiple OpenAI models suitable for invoice processing
    - JSON schema enforcement for consistent output structure

API Integration:
    Uses OpenAI's Responses API which provides structured output generation with
    built-in JSON formatting and better cost efficiency compared to traditional
    chat completions.

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import openai
import json
import datetime
import uuid
import time
import os
from config_loader import OPENAI_API_KEY, OPENAI_MODEL, VAT_RATES, DEFAULT_VAT_RATE
from app_logger import app_logger

if OPENAI_API_KEY:
    client = openai.OpenAI(api_key=OPENAI_API_KEY)
else:
    client = None
    app_logger.warning("OpenAI client not initialized. API key missing.")

# Token costs for different models (only suitable models for invoice processing)
TOKEN_COSTS = {
    # GPT-4.1 family - cost-effective for invoice processing
    "gpt-4.1": {"input": 0.00015/1000, "output": 0.0006/1000},
    "gpt-4.1-nano": {"input": 0.00005/1000, "output": 0.0002/1000},
    "gpt-4.1-mini": {"input": 0.0001/1000, "output": 0.0004/1000},
    "gpt-4.1-2025-04-14": {"input": 0.00015/1000, "output": 0.0006/1000},
    "gpt-4.1-nano-2025-04-14": {"input": 0.00005/1000, "output": 0.0002/1000},
    "gpt-4.1-mini-2025-04-14": {"input": 0.0001/1000, "output": 0.0004/1000},
    
    # GPT-4o family - excellent for structured data extraction
    "gpt-4o": {"input": 0.005/1000, "output": 0.015/1000},
    "gpt-4o-mini": {"input": 0.00015/1000, "output": 0.0006/1000},
    "gpt-4o-2024-11-20": {"input": 0.005/1000, "output": 0.015/1000},
    "gpt-4o-2024-08-06": {"input": 0.005/1000, "output": 0.015/1000},
    "gpt-4o-2024-05-13": {"input": 0.005/1000, "output": 0.015/1000},
    "gpt-4o-mini-2024-07-18": {"input": 0.00015/1000, "output": 0.0006/1000},
    
    # O1 reasoning models - for complex invoice analysis
    "o1-mini": {"input": 0.003/1000, "output": 0.012/1000},
    "o1-mini-2024-09-12": {"input": 0.003/1000, "output": 0.012/1000},
    
    # O3 mini reasoning models
    "o3-mini": {"input": 0.003/1000, "output": 0.012/1000},
    "o3-mini-2025-01-31": {"input": 0.003/1000, "output": 0.012/1000}
}

def calculate_cost(model, input_tokens, output_tokens):
    """
    Calculate the approximate cost of an OpenAI API call based on token usage.
    
    This function computes the estimated cost for a given model and token usage,
    using the current pricing structure for each supported model. Costs are
    calculated separately for input and output tokens.
    
    Args:
        model (str): The OpenAI model name used for the API call.
        input_tokens (int): Number of tokens in the input/prompt.
        output_tokens (int): Number of tokens in the generated response.
        
    Returns:
        float: Total estimated cost in USD for the API call.
        
    Note:
        If the model is not found in TOKEN_COSTS, defaults to gpt-4o-mini pricing.
        Pricing is based on current OpenAI rates and may need updating when rates change.
    """
    costs = TOKEN_COSTS.get(model, TOKEN_COSTS.get("gpt-4o-mini"))
    input_cost = input_tokens * costs["input"]
    output_cost = output_tokens * costs["output"]
    return input_cost + output_cost

def log_responses_api_interaction(session_id, request_data, response_data):
    """
    Log OpenAI Responses API interaction in structured JSON format.
    
    This function creates a comprehensive log entry for each API interaction,
    including request metadata, response details, usage statistics, and cost estimates.
    The structured logging enables detailed analysis of API usage patterns and costs.
    
    Args:
        session_id (str): Unique identifier for the processing session.
        request_data (dict): Dictionary containing request parameters and metadata.
        response_data (dict): Dictionary containing response data and metadata.
        
    Returns:
        None: Function performs logging side effects.
        
    Raises:
        Exception: Logging errors are caught and logged separately to prevent
                  disruption of the main processing flow.
                  
    Note:
        The log entry includes timestamps, model information, token usage,
        cost estimates, and response status for comprehensive audit trails.
    """
    try:
        # Ensure we have dictionaries to work with
        if not isinstance(request_data, dict):
            request_data = {}
        if not isinstance(response_data, dict):
            response_data = {}
            
        response_log = {
            "timestamp": datetime.datetime.now().isoformat(),
            "session_id": session_id,
            "api_type": "responses_api",
            "request_metadata": {
                "model": request_data.get("model"),
                "input_length": len(str(request_data.get("input", ""))),
                "instructions_length": len(str(request_data.get("instructions", ""))),
                "temperature": request_data.get("temperature"),
                "max_output_tokens": request_data.get("max_output_tokens"),
                "store": request_data.get("store")
            },
            "response_metadata": {
                "id": response_data.get("id"),
                "model": response_data.get("model"),
                "status": response_data.get("status"),
                "output_count": len(response_data.get("output", [])),
                "text_format": response_data.get("text", {}).get("format") if isinstance(response_data.get("text"), dict) else None
            },
            "usage_data": response_data.get("usage", {}),
            "cost_estimate": calculate_cost(
                request_data.get("model"),
                response_data.get("usage", {}).get("input_tokens", 0),
                response_data.get("usage", {}).get("output_tokens", 0)
            )
        }
        
        app_logger.info(f"RESPONSES_API_LOG: {json.dumps(response_log)}")
        
    except Exception as e:
        app_logger.error(f"Failed to log Responses API interaction: {e}")

def create_responses_api_request(input_text, instructions, model, session_id, filename):
    """
    Create and execute an OpenAI Responses API request with comprehensive tracking.
    
    This function constructs a properly formatted request to OpenAI's Responses API,
    executes the request, and logs the interaction. It handles the API response
    conversion to a structured format for consistent processing.
    
    Args:
        input_text (str): The PDF text content to process for data extraction.
        instructions (str): Detailed instructions for the AI model on how to
                           extract and format the invoice data.
        model (str): The OpenAI model name to use for processing.
        session_id (str): Unique session identifier for tracking purposes.
        filename (str): Name of the invoice file being processed.
        
    Returns:
        openai.Response: The complete OpenAI API response object containing
                        extracted data, usage statistics, and metadata.
                        
    Raises:
        openai.APIError: For various OpenAI API errors.
        Exception: For request preparation or response processing errors.
        
    Note:
        - Uses JSON object format for structured output
        - Includes comprehensive metadata for tracking and analysis
        - Stores responses in OpenAI's system for potential future reference
        - Temperature set to 0.1 for consistent, deterministic output
    """
    
    # Prepare request data
    request_data = {
        "input": input_text,
        "model": model,
        "instructions": instructions,
        "temperature": 0.1,
        "max_output_tokens": 1000,
        "text": {
            "format": {
                "type": "json_object"
            }
        },
        "user": f"macinvoicer-session-{session_id}",
        "store": True,
        "metadata": {
            "session_id": session_id,
            "filename": filename,
            "application": "MacInvoicer",
            "request_type": "invoice-extraction",
            "timestamp": datetime.datetime.now().isoformat()
        }
    }
    
    # Make Responses API call
    response = client.responses.create(
        input=input_text,
        model=model,
        instructions=instructions,
        temperature=0.1,
        max_output_tokens=1000,
        text={
            "format": {
                "type": "json_object"
            }
        },
        user=f"macinvoicer-session-{session_id}",
        store=True,
        metadata={
            "session_id": session_id,
            "filename": filename,
            "application": "MacInvoicer",
            "request_type": "invoice-extraction",
            "timestamp": datetime.datetime.now().isoformat()
        }
    )
    
    # Convert response to dictionary for logging
    response_data = {
        "id": response.id,
        "object": response.object,
        "created_at": response.created_at,
        "status": response.status,
        "model": response.model,
        "output": [
            {
                "id": output.id,
                "type": output.type,
                "status": getattr(output, 'status', None),
                "role": getattr(output, 'role', None),
                "content": [
                    {
                        "type": content.type,
                        "text": getattr(content, 'text', None)
                    }
                    for content in getattr(output, 'content', [])
                ]
            }
            for output in response.output
        ],
        "usage": {
            "input_tokens": response.usage.input_tokens,
            "output_tokens": response.usage.output_tokens,
            "total_tokens": response.usage.total_tokens
        },
        "text": {
            "format": response.text.format.type if response.text and response.text.format else None
        },
        "temperature": response.temperature,
        "store": response.store,
        "metadata": response.metadata
    }
    
    # Log the interaction
    log_responses_api_interaction(session_id, request_data, response_data)
    
    return response

def upload_pdf_to_openai(pdf_path, session_id):
    """
    Upload a PDF file to OpenAI for direct processing with Responses API.
    
    This function uploads a PDF file to OpenAI's Files API, making it available
    for processing by the Responses API. The uploaded file can then be referenced
    in responses API calls for direct PDF analysis.
    
    Args:
        pdf_path (str): Absolute path to the PDF file to upload.
        session_id (str): Unique session identifier for tracking purposes.
        
    Returns:
        dict: Upload result containing:
            - success (bool): Whether upload was successful
            - file_id (str): OpenAI file ID if successful
            - error (str): Error message if failed
            - file_size (int): Size of uploaded file in bytes
            
    Raises:
        openai.APIError: For various OpenAI API errors.
        Exception: For file access or other unexpected errors.
        
    Note:
        - Files are uploaded with purpose 'user_data' for Responses API compatibility
        - File size limits apply based on OpenAI's current restrictions (100 pages, 32MB)
        - Uploaded files may be automatically deleted by OpenAI after processing
    """
    if not client:
        app_logger.error(f"[{session_id}] OpenAI client not available for file upload")
        return {"success": False, "error": "OpenAI client not configured", "file_id": None}
    
    try:
        # Check file exists and get size
        if not os.path.exists(pdf_path):
            app_logger.error(f"[{session_id}] PDF file not found: {pdf_path}")
            return {"success": False, "error": "File not found", "file_id": None}
        
        file_size = os.path.getsize(pdf_path)
        app_logger.info(f"[{session_id}] Uploading PDF to OpenAI: {os.path.basename(pdf_path)} ({file_size} bytes)")
        
        # Upload file to OpenAI with user_data purpose for Responses API
        with open(pdf_path, 'rb') as file:
            upload_response = client.files.create(
                file=file,
                purpose='user_data'  # Required purpose for Responses API file uploads
            )
        
        file_id = upload_response.id
        app_logger.info(f"[{session_id}] PDF uploaded successfully. File ID: {file_id}")
        
        return {
            "success": True,
            "file_id": file_id,
            "error": None,
            "file_size": file_size
        }
        
    except openai.APIError as e:
        app_logger.error(f"[{session_id}] OpenAI API error during file upload: {e}", exc_info=True)
        return {"success": False, "error": f"API error: {str(e)}", "file_id": None}
    except Exception as e:
        app_logger.error(f"[{session_id}] Unexpected error during file upload: {e}", exc_info=True)
        return {"success": False, "error": f"Upload error: {str(e)}", "file_id": None}

def extract_invoice_data_with_file(pdf_path, filename, session_id):
    """
    Extract invoice data using OpenAI's Responses API with PDF file upload.
    
    This function uploads the PDF file to OpenAI and processes it directly using
    the Responses API with file attachment. This approach can provide better 
    accuracy for complex layouts, scanned documents, or cases where text 
    extraction might miss important visual elements.
    
    Args:
        pdf_path (str): Absolute path to the PDF file to process.
        filename (str): Name of the invoice file being processed.
        session_id (str): Unique session identifier for tracking purposes.
        
    Returns:
        dict: Structured invoice data with the same format as text-based extraction,
              plus additional metadata about the file-based processing.
              
    Raises:
        openai.APIError: For various OpenAI API errors.
        Exception: For file processing or other unexpected errors.
        
    Note:
        - Uses Responses API with file input (same as text-based approach)
        - Requires models that support file inputs (e.g., GPT-4.1, GPT-4o variants)
        - May have different cost structure compared to text-only processing
    """
    if not client:
        app_logger.error(f"[{session_id}] OpenAI client not available for file processing")
        return {
            "invoice_id": "ERROR_CLIENT_INIT",
            "vendor_name": "OpenAI Client Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": "OpenAI client not configured for file processing."
        }
    
    app_logger.info(f"[{session_id}] Starting file-based extraction for {filename}")
    
    # Upload PDF file
    upload_result = upload_pdf_to_openai(pdf_path, session_id)
    if not upload_result["success"]:
        app_logger.error(f"[{session_id}] File upload failed: {upload_result['error']}")
        return {
            "invoice_id": "ERROR_FILE_UPLOAD",
            "vendor_name": "File Upload Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"File upload failed: {upload_result['error']}. Session: {session_id}"
        }
    
    file_id = upload_result["file_id"]
    
    # Instructions for the Responses API (same as text-based processing)
    vat_rates_str = ", ".join([f"{rate}%" for rate in VAT_RATES])
    instructions = (
        "You are an expert invoice data extraction specialist. Extract ONLY clearly identifiable data from the PDF invoice. "
        "STRICT RULE: If you cannot clearly identify a field value, use null. DO NOT guess, assume, or provide defaults. "
        "CRITICAL OCR ERROR WARNING: When you see patterns like 'XX YYY.YY ZZZ.ZZ' on invoice totals lines, "
        "DO NOT interpret this as XXYYY.YY. The correct interpretation is: "
        "- XX = quantity or line number (IGNORE for total_amount) "
        "- YYY.YY = the actual total amount (USE this for total_amount) "
        "- ZZZ.ZZ = VAT amount "
        "EXTRACTION REQUIREMENTS: "
        "- invoice_id: Extract ONLY if you see a clear invoice/reference number "
        "- vendor_name: Extract ONLY if you can clearly identify the company name from headers/letterhead "
        "- invoice_date: Extract ONLY if you see a clear date, format as YYYY-MM-DD "
        "- due_date: Extract ONLY if explicitly stated, format as YYYY-MM-DD "
        "- subtotal: Extract the amount before VAT/taxes if clearly stated. "
        "- vat_amount: Extract ONLY if VAT/tax amount is clearly identified "
        "- vat_rate: Extract the VAT/tax rate percentage if clearly stated. Look for patterns like '23%', 'VAT 13.5%', 'Tax Rate: 9%', etc. "
        f"  Common rates to look for: {vat_rates_str}. Return as a number (e.g., 23 for 23%). "
        "- total_amount: Extract ONLY the final total amount, not line items or concatenated OCR errors "
        "- currency: Extract ONLY if clearly stated (EUR, USD, GBP, etc.) or shown with symbols (€, $, £) "
        "- business_unit: Extract department, cost center, business unit, project code, or organizational identifier if present "
        "- is_duplicate: Determine if the invoice is marked as a duplicate or copy (true/false). "
        "Return a JSON object with these exact fields: "
        '"invoice_id": string or null, '
        '"vendor_name": string or null, '
        '"invoice_date": string (YYYY-MM-DD) or null, '
        '"due_date": string (YYYY-MM-DD) or null, '
        '"subtotal": float or null, '
        '"vat_amount": float or null, '
        '"vat_rate": float or null, '
        '"total_amount": float or null, '
        '"currency": string (3-letter code) or null, '
        '"business_unit": string or null, '
        '"is_duplicate": boolean or null. '
        "REMEMBER: When in doubt, use null. Only extract what is absolutely clear from the document."
    )

    try:
        start_time = datetime.datetime.now()
        app_logger.info(f"[{session_id}] Processing uploaded file with Responses API...")
        
        # Create Responses API request with file input
        response = client.responses.create(
            model=OPENAI_MODEL,
            input=[
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "input_file",
                            "file_id": file_id
                        },
                        {
                            "type": "input_text",
                            "text": "Extract invoice data from this PDF document and return the result as a JSON object."
                        }
                    ]
                }
            ],
            instructions=instructions,
            temperature=0.1,
            max_output_tokens=1000,
            text={
                "format": {
                    "type": "json_object"
                }
            },
            user=f"macinvoicer-session-{session_id}",
            store=True,
            metadata={
                "session_id": session_id,
                "filename": filename,
                "application": "MacInvoicer",
                "request_type": "invoice-extraction-file",
                "timestamp": datetime.datetime.now().isoformat()
            }
        )
        
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()

        # Extract response content from the Responses API format
        if not response.output or len(response.output) == 0:
            app_logger.error(f"[{session_id}] No output received from Responses API")
            return {
                "invoice_id": "ERROR_NO_OUTPUT",
                "vendor_name": "No API Output",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": f"No output from Responses API. Session: {session_id}"
            }

        # Get the text content from the first output message
        output_item = response.output[0]
        if not hasattr(output_item, 'content') or len(output_item.content) == 0:
            app_logger.error(f"[{session_id}] No content in output item")
            return {
                "invoice_id": "ERROR_NO_CONTENT",
                "vendor_name": "No Content",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": f"No content in output. Session: {session_id}"
            }

        response_content = output_item.content[0].text
        response_id = response.id
        
        # Token usage and cost calculation
        usage = response.usage
        input_tokens = usage.input_tokens
        output_tokens = usage.output_tokens
        total_tokens = usage.total_tokens
        estimated_cost = calculate_cost(OPENAI_MODEL, input_tokens, output_tokens)

        # Log API interaction details
        app_logger.info(f"[{session_id}] File-based Responses API completed successfully")
        app_logger.info(f"[{session_id}] Response ID: {response_id}")
        app_logger.info(f"[{session_id}] Status: {response.status}")
        app_logger.info(f"[{session_id}] Duration: {duration:.2f}s")
        app_logger.info(f"[{session_id}] Tokens - Input: {input_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        app_logger.info(f"[{session_id}] Cost: ${estimated_cost:.6f}")
        app_logger.debug(f"[{session_id}] Response content: {response_content}")

        # Log usage data for analysis
        usage_log = {
            "timestamp": start_time.isoformat(),
            "session_id": session_id,
            "filename": filename,
            "model": OPENAI_MODEL,
            "response_id": response_id,
            "status": response.status,
            "duration_seconds": duration,
            "tokens": {
                "input": input_tokens,
                "output": output_tokens,
                "total": total_tokens
            },
            "cost_usd": estimated_cost,
            "file_size_bytes": upload_result["file_size"],
            "api_type": "responses_api_with_file"
        }
        app_logger.info(f"[{session_id}] FILE_USAGE_DATA: {json.dumps(usage_log)}")

        # Parse JSON response
        try:
            extracted_json = json.loads(response_content)
        except json.JSONDecodeError as e:
            app_logger.error(f"[{session_id}] JSON decode error in file processing: {e}. Response: {response_content[:500]}", exc_info=True)
            return {
                "invoice_id": "ERROR_JSON_DECODE_FILE",
                "vendor_name": "File Response Parse Error",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.1,
                "processing_notes": f"JSON decode failed in file processing. Session: {session_id}, Response: {response_id}"
            }

        # Process extracted data
        # Currency conversion (ensure EUR for amounts)
        original_currency = extracted_json.get("currency", "EUR").upper()
        total_amount_original = extracted_json.get("total_amount")
        vat_amount_original = extracted_json.get("vat_amount")
        subtotal_original = extracted_json.get("subtotal")

        conversion_rate = 1.0
        if original_currency != "EUR":
            app_logger.warning(f"[{session_id}] Currency {original_currency} is not EUR (file processing). FX conversion not implemented. Assuming 1:1.")
        
        total_amount_eur = None
        if isinstance(total_amount_original, (int, float)):
            total_amount_eur = round(total_amount_original * conversion_rate, 2)

        vat_amount_eur = None
        if isinstance(vat_amount_original, (int, float)):
            vat_amount_eur = round(vat_amount_original * conversion_rate, 2)
        
        subtotal_eur = None
        if isinstance(subtotal_original, (int, float)):
            subtotal_eur = round(subtotal_original * conversion_rate, 2)

        # Create processing notes with file processing tracking information
        processing_notes = (f"Processed via OpenAI Responses API with file upload. "
                          f"Model: {OPENAI_MODEL}, Session: {session_id}, "
                          f"Response: {response_id}, Status: {response.status}, File ID: {file_id}, "
                          f"Tokens: {total_tokens}, Cost: ${estimated_cost:.6f}, "
                          f"Duration: {duration:.2f}s")

        # Process VAT rate
        vat_rate = extracted_json.get("vat_rate")
        if vat_rate is None and vat_amount_eur and subtotal_eur and subtotal_eur > 0:
            # Calculate VAT rate if not detected but amounts are available
            calculated_rate = (vat_amount_eur / subtotal_eur) * 100
            # Check if calculated rate matches any configured rates (within 0.1% tolerance)
            for configured_rate in VAT_RATES:
                if abs(calculated_rate - configured_rate) <= 0.1:
                    vat_rate = configured_rate
                    break
            if vat_rate is None:
                vat_rate = round(calculated_rate, 1)
        elif vat_rate is not None:
            # Validate detected rate against configured rates
            if vat_rate not in VAT_RATES:
                # Find closest configured rate
                closest_rate = min(VAT_RATES, key=lambda x: abs(x - vat_rate))
                if abs(closest_rate - vat_rate) <= 1.0:  # Within 1% tolerance
                    vat_rate = closest_rate

        result = {
            "invoice_id": extracted_json.get("invoice_id"),
            "vendor_name": extracted_json.get("vendor_name"),
            "invoice_date": extracted_json.get("invoice_date"),
            "due_date": extracted_json.get("due_date"),
            "subtotal_eur": subtotal_eur,
            "vat_amount_eur": vat_amount_eur,
            "vat_rate": vat_rate,
            "total_amount_eur": total_amount_eur,
            "business_unit": extracted_json.get("business_unit"),
            "original_currency": original_currency if original_currency else "EUR",
            "original_total_amount": total_amount_original,
            "original_subtotal": subtotal_original,
            "original_vat_amount": vat_amount_original,
            "is_duplicate": extracted_json.get("is_duplicate", False),
            "processing_notes_ref": "",
            "ai_confidence_score": 0.9,  # Placeholder, refine later. File processing might have higher confidence.
            "processing_notes": processing_notes,
            "responses_api_session_id": session_id,
            "responses_api_response_id": response_id,
            "responses_api_model_used": OPENAI_MODEL,
            "responses_api_input_tokens": input_tokens,
            "responses_api_output_tokens": output_tokens,
            "responses_api_total_tokens": total_tokens,
            "responses_api_estimated_cost_usd": estimated_cost,
            "responses_api_file_id": file_id,
            "responses_api_file_size_bytes": upload_result.get("file_size")
        }

        app_logger.info(f"[{session_id}] Successfully extracted via file-based Responses API: {json.dumps({k: v for k, v in result.items() if k not in ['processing_notes']})}")
        return result
        
    except openai.APIError as e:
        app_logger.error(f"[{session_id}] OpenAI API error in file processing: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_API_FILE",
            "vendor_name": "File API Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"File processing API error. Session: {session_id}. Error: {str(e)}"
        }
    except Exception as e:
        app_logger.error(f"[{session_id}] Unexpected error in file processing: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_UNEXPECTED_FILE",
            "vendor_name": "File Processing Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Unexpected file processing error. Session: {session_id}. Error: {str(e)}"
        }

def compare_extraction_results(text_result, file_result, session_id):
    """
    Compare and merge results from text-based and file-based extraction methods.
    
    This function analyzes the results from both extraction approaches to determine
    the most reliable data and calculate an overall confidence score. It compares
    key fields and identifies discrepancies that might require manual review.
    
    Args:
        text_result (dict or None): Results from text-based extraction.
        file_result (dict or None): Results from file-based extraction.
        session_id (str): Unique session identifier for tracking purposes.
        
    Returns:
        dict: Merged result with enhanced confidence scoring and comparison metadata.
        
    Note:
        - Prioritizes file-based results when both methods succeed
        - Falls back to text-based results if file processing fails
        - Flags significant discrepancies for manual review
        - Calculates confidence based on agreement between methods
        - Handles None results from failed extractions
    """
    app_logger.info(f"[{session_id}] Comparing extraction results from both methods")
    
    # Handle None results from failed extractions
    if text_result is None:
        app_logger.warning(f"[{session_id}] Text extraction returned None")
        text_result = {
            "invoice_id": "ERROR_TEXT_NONE",
            "vendor_name": "Text Extraction Failed",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": "Text extraction returned None"
        }
    
    if file_result is None:
        app_logger.warning(f"[{session_id}] File extraction returned None")
        file_result = {
            "invoice_id": "ERROR_FILE_NONE",
            "vendor_name": "File Extraction Failed",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": "File extraction returned None"
        }
    
    # Check if either method failed completely
    text_invoice_id = text_result.get("invoice_id") if text_result else None
    file_invoice_id = file_result.get("invoice_id") if file_result else None
    
    text_failed = text_invoice_id is None or str(text_invoice_id).startswith("ERROR_")
    file_failed = file_invoice_id is None or str(file_invoice_id).startswith("ERROR_")
    
    if text_failed and file_failed:
        app_logger.error(f"[{session_id}] Both extraction methods failed")
        return {
            "invoice_id": "ERROR_BOTH_METHODS",
            "vendor_name": "Both Methods Failed",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Both text and file extraction methods failed. Session: {session_id}",
            "comparison_notes": "Both methods failed - no comparison possible"
        }
    
    # If only one method succeeded, use that result
    if text_failed and not file_failed:
        app_logger.info(f"[{session_id}] Using file-based result (text method failed)")
        result = file_result.copy()
        result["comparison_notes"] = "Text extraction failed - using file-based result only"
        result["ai_confidence_score"] = 0.8  # Slightly lower confidence when only one method works
        return result
    
    if file_failed and not text_failed:
        app_logger.info(f"[{session_id}] Using text-based result (file method failed)")
        result = text_result.copy()
        result["comparison_notes"] = "File extraction failed - using text-based result only"
        result["ai_confidence_score"] = 0.8  # Slightly lower confidence when only one method works
        return result
    
    # Both methods succeeded - compare results
    app_logger.info(f"[{session_id}] Both methods succeeded - comparing results")
    
    # Compare key fields
    comparisons = {}
    key_fields = ["invoice_id", "vendor_name", "invoice_date", "total_amount_eur", "vat_amount_eur"]
    
    for field in key_fields:
        text_val = text_result.get(field)
        file_val = file_result.get(field)
        
        if text_val == file_val:
            comparisons[field] = "match"
        elif text_val is None and file_val is not None:
            comparisons[field] = "file_has_value"
        elif text_val is not None and file_val is None:
            comparisons[field] = "text_has_value"
        else:
            comparisons[field] = "different"
    
    # Calculate agreement score
    matches = sum(1 for status in comparisons.values() if status == "match")
    total_comparisons = len(key_fields)
    agreement_score = matches / total_comparisons
    
    app_logger.info(f"[{session_id}] Field agreement score: {agreement_score:.2f} ({matches}/{total_comparisons})")
    
    # Determine primary result (prefer file-based when both succeed)
    primary_result = file_result.copy()
    
    # Fill in missing values from text result
    for field in key_fields:
        if primary_result.get(field) is None and text_result.get(field) is not None:
            primary_result[field] = text_result[field]
            app_logger.debug(f"[{session_id}] Filled {field} from text result: {text_result[field]}")
    
    # Calculate final confidence score
    base_confidence = 0.95  # High confidence when both methods work
    if agreement_score >= 0.8:
        final_confidence = base_confidence
    elif agreement_score >= 0.6:
        final_confidence = base_confidence * 0.9
    elif agreement_score >= 0.4:
        final_confidence = base_confidence * 0.8
    else:
        final_confidence = base_confidence * 0.7
    
    # Create comparison notes
    comparison_details = []
    for field, status in comparisons.items():
        if status == "different":
            text_val = text_result.get(field)
            file_val = file_result.get(field)
            comparison_details.append(f"{field}: text='{text_val}' vs file='{file_val}'")
        elif status in ["file_has_value", "text_has_value"]:
            comparison_details.append(f"{field}: {status}")
    
    comparison_notes = f"Agreement: {agreement_score:.2f}. "
    if comparison_details:
        comparison_notes += f"Differences: {'; '.join(comparison_details)}"
    else:
        comparison_notes += "All fields match between methods."
    
    # Update result with comparison metadata
    primary_result["ai_confidence_score"] = final_confidence
    primary_result["comparison_notes"] = comparison_notes
    primary_result["field_agreement_score"] = agreement_score
    primary_result["extraction_methods_used"] = "text_and_file"
    
    # Combine processing notes
    text_notes = text_result.get("processing_notes", "")
    file_notes = file_result.get("processing_notes", "")
    combined_notes = f"HYBRID PROCESSING: Text method: {text_notes} | File method: {file_notes}"
    primary_result["processing_notes"] = combined_notes
    
    app_logger.info(f"[{session_id}] Hybrid extraction completed. Final confidence: {final_confidence:.2f}")
    return primary_result

def extract_invoice_data_hybrid(pdf_path, filename):
    """
    Extract invoice data using both text-based and file-based approaches for enhanced confidence.
    
    This function orchestrates both extraction methods and combines their results to provide
    the most accurate and confident invoice data extraction. It uses the existing text
    extraction pipeline alongside the new file upload approach.
    
    Args:
        pdf_path (str): Absolute path to the PDF file to process.
        filename (str): Name of the invoice file being processed.
        
    Returns:
        dict: Structured invoice data with enhanced confidence scoring and comparison metadata.
        
    Note:
        - Runs both extraction methods in parallel conceptually
        - Compares results and provides detailed analysis
        - Falls back gracefully if one method fails
        - Provides comprehensive logging for both approaches
    """
    # Generate unique session ID for this hybrid processing
    session_id = str(uuid.uuid4())[:8]
    app_logger.info(f"[{session_id}] Starting hybrid extraction for {filename}")
    
    # First, extract text from PDF (reusing existing logic)
    from invoice_parser import extract_text_from_pdf
    pdf_text = extract_text_from_pdf(pdf_path)
    
    app_logger.debug(f"[{session_id}] PDF text extraction result: {len(pdf_text) if pdf_text else 'None'} characters")
    
    if not pdf_text:
        app_logger.warning(f"[{session_id}] Could not extract text from PDF. Falling back to file-only processing.")
        # If text extraction fails, try file-only approach
        return extract_invoice_data_with_file(pdf_path, filename, session_id)
    
    # Run both extraction methods
    app_logger.info(f"[{session_id}] Running text-based extraction...")
    try:
        text_result = extract_invoice_data_openai(pdf_text, filename)
        app_logger.debug(f"[{session_id}] Text extraction result type: {type(text_result)}")
        if text_result is None:
            app_logger.error(f"[{session_id}] Text extraction returned None - creating error result")
            text_result = {
                "invoice_id": "ERROR_TEXT_NONE",
                "vendor_name": "Text Extraction Failed",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": "Text extraction returned None"
            }
    except Exception as e:
        app_logger.error(f"[{session_id}] Exception in text extraction: {e}", exc_info=True)
        text_result = {
            "invoice_id": "ERROR_TEXT_EXCEPTION",
            "vendor_name": "Text Extraction Exception",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Text extraction exception: {str(e)}"
        }
    
    app_logger.info(f"[{session_id}] Running file-based extraction...")
    try:
        file_result = extract_invoice_data_with_file(pdf_path, filename, session_id)
        app_logger.debug(f"[{session_id}] File extraction result type: {type(file_result)}")
        if file_result is None:
            app_logger.error(f"[{session_id}] File extraction returned None - creating error result")
            file_result = {
                "invoice_id": "ERROR_FILE_NONE",
                "vendor_name": "File Extraction Failed",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": "File extraction returned None"
            }
    except Exception as e:
        app_logger.error(f"[{session_id}] Exception in file extraction: {e}", exc_info=True)
        file_result = {
            "invoice_id": "ERROR_FILE_EXCEPTION",
            "vendor_name": "File Extraction Exception",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"File extraction exception: {str(e)}"
        }
    
    # Compare and merge results
    app_logger.info(f"[{session_id}] Comparing and merging results...")
    app_logger.debug(f"[{session_id}] Text result type: {type(text_result)}, File result type: {type(file_result)}")
    app_logger.debug(f"[{session_id}] Text result keys: {list(text_result.keys()) if text_result else 'None'}")
    app_logger.debug(f"[{session_id}] File result keys: {list(file_result.keys()) if file_result else 'None'}")
    
    final_result = compare_extraction_results(text_result, file_result, session_id)
    
    app_logger.info(f"[{session_id}] Hybrid extraction completed for {filename}")
    return final_result

def extract_invoice_data_openai(pdf_text_content, filename):
    """
    Extract structured invoice data from PDF text using OpenAI's Responses API.
    
    This is the main function for AI-powered invoice data extraction. It processes
    PDF text content through OpenAI's API to extract structured invoice information
    including vendor details, amounts, dates, and other relevant data.
    
    The function handles the complete extraction pipeline:
    1. Input validation and preprocessing
    2. API request preparation and execution
    3. Response parsing and data structuring
    4. Error handling and fallback responses
    5. Cost calculation and usage logging
    
    Args:
        pdf_text_content (str): Raw text content extracted from the PDF invoice.
        filename (str): Name of the invoice file being processed.
        
    Returns:
        dict: Structured invoice data containing:
            - invoice_id: Invoice reference number
            - vendor_name: Name of the issuing company
            - invoice_date: Invoice date in YYYY-MM-DD format
            - due_date: Payment due date or None
            - total_amount_eur: Total amount in EUR
            - vat_amount_eur: VAT amount in EUR
            - original_currency: Original currency code
            - original_total_amount: Amount in original currency
            - ai_confidence_score: AI extraction confidence (0-1)
            - processing_notes: Detailed processing information
            - responses_api_*: API tracking fields
            
    Raises:
        openai.RateLimitError: When API rate limits are exceeded.
        openai.AuthenticationError: When API key is invalid.
        openai.APIError: For general API errors.
        Exception: For unexpected errors during processing.
        
    Note:
        - Handles text truncation for very long PDFs (max 12,000 chars)
        - Returns error codes in the response for various failure scenarios
        - Includes comprehensive cost tracking and usage statistics
        - Generates unique session IDs for tracking individual requests
    """
    if not client:
        app_logger.error("OpenAI client is not available. Cannot process invoice.")
        return {
            "invoice_id": "ERROR_CLIENT_INIT",
            "vendor_name": "OpenAI Client Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": "OpenAI client not configured or API key missing."
        }

    # Generate unique session ID
    session_id = str(uuid.uuid4())[:8]
    app_logger.info(f"[{session_id}] Starting OpenAI Responses API processing for {filename}")

    # Input validation and preprocessing
    max_text_length = 12000 
    text_to_send = pdf_text_content
    if len(pdf_text_content) > max_text_length:
        app_logger.warning(f"[{session_id}] Text content is very long ({len(pdf_text_content)} chars). Truncating to {max_text_length}.")
        text_to_send = pdf_text_content[:max_text_length]
    elif not pdf_text_content or pdf_text_content.isspace():
        app_logger.warning(f"[{session_id}] PDF text content is empty. Skipping API call.")
        return {
            "invoice_id": "ERROR_EMPTY_CONTENT",
            "vendor_name": "Empty PDF Content",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"PDF content was empty. Session: {session_id}"
        }
    
    app_logger.info(f"[{session_id}] Preparing Responses API request - Model: {OPENAI_MODEL}, Input: {len(text_to_send)} chars")
    app_logger.debug(f"[{session_id}] Input preview: {text_to_send[:200].replace(chr(10), ' ')}")

    # Instructions for the Responses API (replaces system message)
    vat_rates_str = ", ".join([f"{rate}%" for rate in VAT_RATES])
    instructions = (
        "You are an expert invoice data extraction specialist. Extract ONLY clearly identifiable data from invoice text. "
        "STRICT RULE: If you cannot clearly identify a field value from the text, use null. DO NOT guess, assume, or provide defaults. "
        "CRITICAL OCR ERROR WARNING: When you see patterns like 'XX YYY.YY ZZZ.ZZ' on invoice totals lines, "
        "DO NOT interpret this as XXYYY.YY. The correct interpretation is: "
        "- XX = quantity or line number (IGNORE for total_amount) "
        "- YYY.YY = the actual total amount (USE this for total_amount) "
        "- ZZZ.ZZ = VAT amount "
        "EXTRACTION REQUIREMENTS: "
        "- invoice_id: Extract ONLY if you see a clear invoice/reference number in the text "
        "- vendor_name: Extract ONLY if you can clearly identify the company name from headers/letterhead "
        "- invoice_date: Extract ONLY if you see a clear date in the text, format as YYYY-MM-DD "
        "- due_date: Extract ONLY if explicitly stated, format as YYYY-MM-DD "
        "- subtotal: Extract the amount before VAT/taxes if clearly stated. "
        "- vat_amount: Extract ONLY if VAT/tax amount is clearly identified "
        "- vat_rate: Extract the VAT/tax rate percentage if clearly stated in the text. Look for patterns like '23%', 'VAT 13.5%', 'Tax Rate: 9%', etc. "
        f"  Common rates to look for: {vat_rates_str}. Return as a number (e.g., 23 for 23%). "
        "- total_amount: Extract ONLY the final total amount, not line items or concatenated OCR errors "
        "- currency: Extract ONLY if clearly stated (EUR, USD, GBP, etc.) or shown with symbols (€, $, £) "
        "- business_unit: Extract department, cost center, business unit, project code, or organizational identifier if present "
        "- is_duplicate: Determine if the invoice is marked as a duplicate or copy (true/false). "
        "Return a JSON object with these exact fields: "
        '"invoice_id": string or null, '
        '"vendor_name": string or null, '
        '"invoice_date": string (YYYY-MM-DD) or null, '
        '"due_date": string (YYYY-MM-DD) or null, '
        '"subtotal": float or null, '
        '"vat_amount": float or null, '
        '"vat_rate": float or null, '
        '"total_amount": float or null, '
        '"currency": string (3-letter code) or null, '
        '"business_unit": string or null, '
        '"is_duplicate": boolean or null. '
        "REMEMBER: When in doubt, use null. Only extract what is absolutely clear from the text."
    )

    # Input text for the model - MUST include "json" for json_object format
    input_text = f"Extract invoice data from the following text and return the result as a JSON object:\n\n{text_to_send}"

    try:
        start_time = datetime.datetime.now()
        app_logger.info(f"[{session_id}] Calling OpenAI Responses API...")
        
        # Make Responses API request
        response = create_responses_api_request(
            input_text=input_text,
            instructions=instructions,
            model=OPENAI_MODEL,
            session_id=session_id,
            filename=filename
        )
        
        end_time = datetime.datetime.now()
        duration = (end_time - start_time).total_seconds()

        # Extract response content from the Responses API format
        if not response.output or len(response.output) == 0:
            app_logger.error(f"[{session_id}] No output received from Responses API")
            return {
                "invoice_id": "ERROR_NO_OUTPUT",
                "vendor_name": "No API Output",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": f"No output from Responses API. Session: {session_id}"
            }

        # Get the text content from the first output message
        output_item = response.output[0]
        if not hasattr(output_item, 'content') or len(output_item.content) == 0:
            app_logger.error(f"[{session_id}] No content in output item")
            return {
                "invoice_id": "ERROR_NO_CONTENT",
                "vendor_name": "No Content",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.0,
                "processing_notes": f"No content in output. Session: {session_id}"
            }

        response_content = output_item.content[0].text
        response_id = response.id
        
        # Token usage and cost calculation
        usage = response.usage
        input_tokens = usage.input_tokens
        output_tokens = usage.output_tokens
        total_tokens = usage.total_tokens
        estimated_cost = calculate_cost(OPENAI_MODEL, input_tokens, output_tokens)

        # Log API interaction details
        app_logger.info(f"[{session_id}] Responses API completed successfully")
        app_logger.info(f"[{session_id}] Response ID: {response_id}")
        app_logger.info(f"[{session_id}] Status: {response.status}")
        app_logger.info(f"[{session_id}] Duration: {duration:.2f}s")
        app_logger.info(f"[{session_id}] Tokens - Input: {input_tokens}, Output: {output_tokens}, Total: {total_tokens}")
        app_logger.info(f"[{session_id}] Cost: ${estimated_cost:.6f}")
        app_logger.debug(f"[{session_id}] Response content: {response_content}")

        # Log usage data for analysis
        usage_log = {
            "timestamp": start_time.isoformat(),
            "session_id": session_id,
            "filename": filename,
            "model": OPENAI_MODEL,
            "response_id": response_id,
            "status": response.status,
            "duration_seconds": duration,
            "tokens": {
                "input": input_tokens,
                "output": output_tokens,
                "total": total_tokens
            },
            "cost_usd": estimated_cost,
            "input_chars": len(text_to_send),
            "api_type": "responses_api"
        }
        app_logger.info(f"[{session_id}] USAGE_DATA: {json.dumps(usage_log)}")

        # Parse JSON response
        try:
            parsed_content = json.loads(response_content)
        except json.JSONDecodeError as e:
            app_logger.error(f"[{session_id}] JSON decode error: {e}. Response: {response_content[:500]}", exc_info=True)
            return {
                "invoice_id": "ERROR_JSON_DECODE",
                "vendor_name": "Response Parse Error",
                "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
                "total_amount_eur": 0.0,
                "vat_amount_eur": 0.0,
                "business_unit": None,
                "ai_confidence_score": 0.1,
                "processing_notes": f"JSON decode failed. Session: {session_id}, Response: {response_id}"
            }

        # Currency conversion (ensure EUR for amounts)
        original_currency = parsed_content.get("currency", "EUR").upper()
        total_amount_original = parsed_content.get("total_amount")
        vat_amount_original = parsed_content.get("vat_amount")
        subtotal_original = parsed_content.get("subtotal")

        # Basic currency conversion (replace with actual rates if needed)
        # For simplicity, assume 1:1 if not EUR, or log a warning
        conversion_rate = 1.0
        if original_currency != "EUR":
            app_logger.warning(f"[{session_id}] Currency {original_currency} is not EUR. FX conversion not implemented. Assuming 1:1 for now.")
            # In a real scenario, fetch conversion_rate here
        
        total_amount_eur = None
        if isinstance(total_amount_original, (int, float)):
            total_amount_eur = round(total_amount_original * conversion_rate, 2)

        vat_amount_eur = None
        if isinstance(vat_amount_original, (int, float)):
            vat_amount_eur = round(vat_amount_original * conversion_rate, 2)
        
        subtotal_eur = None
        if isinstance(subtotal_original, (int, float)):
            subtotal_eur = round(subtotal_original * conversion_rate, 2)

        # Process VAT rate
        vat_rate = parsed_content.get("vat_rate")
        if vat_rate is None and vat_amount_eur and subtotal_eur and subtotal_eur > 0:
            # Calculate VAT rate if not detected but amounts are available
            calculated_rate = (vat_amount_eur / subtotal_eur) * 100
            # Check if calculated rate matches any configured rates (within 0.1% tolerance)
            for configured_rate in VAT_RATES:
                if abs(calculated_rate - configured_rate) <= 0.1:
                    vat_rate = configured_rate
                    break
            if vat_rate is None:
                vat_rate = round(calculated_rate, 1)
        elif vat_rate is not None:
            # Validate detected rate against configured rates
            if vat_rate not in VAT_RATES:
                # Find closest configured rate
                closest_rate = min(VAT_RATES, key=lambda x: abs(x - vat_rate))
                if abs(closest_rate - vat_rate) <= 1.0:  # Within 1% tolerance
                    vat_rate = closest_rate

        # Construct final result dictionary
        final_result = {
            "invoice_id": parsed_content.get("invoice_id"),
            "vendor_name": parsed_content.get("vendor_name"),
            "invoice_date": parsed_content.get("invoice_date"),
            "due_date": parsed_content.get("due_date"),
            "subtotal_eur": subtotal_eur,
            "vat_amount_eur": vat_amount_eur,
            "vat_rate": vat_rate,
            "total_amount_eur": total_amount_eur,
            "business_unit": parsed_content.get("business_unit"),
            "original_currency": original_currency if original_currency else "EUR",
            "original_total_amount": total_amount_original,
            "original_subtotal": subtotal_original,
            "original_vat_amount": vat_amount_original,
            "is_duplicate": parsed_content.get("is_duplicate", False),
            "processing_notes_ref": "",
            "ai_confidence_score": 0.85,  # Placeholder, refine later
            "processing_notes": f"Extracted by OpenAI model {OPENAI_MODEL}. Session: {session_id}. Duration: {duration:.2f}s. Cost: ${estimated_cost:.6f}",
            "responses_api_session_id": session_id,
            "responses_api_response_id": response_id,
            "responses_api_model_used": OPENAI_MODEL,
            "responses_api_input_tokens": input_tokens,
            "responses_api_output_tokens": output_tokens,
            "responses_api_total_tokens": total_tokens,
            "responses_api_estimated_cost_usd": estimated_cost
        }

        app_logger.info(f"[{session_id}] Final structured data: {final_result}")
        return final_result

    except openai.RateLimitError as e:
        app_logger.error(f"[{session_id}] OpenAI rate limit exceeded: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_RATE_LIMIT",
            "vendor_name": "Rate Limit Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Rate limit exceeded. Session: {session_id}. Retry later."
        }
    except openai.AuthenticationError as e:
        app_logger.error(f"[{session_id}] OpenAI authentication error: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_AUTH",
            "vendor_name": "Authentication Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Authentication error. Check API key. Session: {session_id}"
        }
    except openai.APIError as e:
        app_logger.error(f"[{session_id}] OpenAI API error: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_API",
            "vendor_name": "API Error",
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"API error. Session: {session_id}. Error: {str(e)}"
        }
    except Exception as e:
        app_logger.error(f"[{session_id}] Unexpected error: {e}", exc_info=True)
        return {
            "invoice_id": "ERROR_UNEXPECTED",
            "vendor_name": "Unexpected Error", 
            "invoice_date": datetime.date.today().strftime("%Y-%m-%d"),
            "total_amount_eur": 0.0,
            "vat_amount_eur": 0.0,
            "business_unit": None,
            "ai_confidence_score": 0.0,
            "processing_notes": f"Unexpected error. Session: {session_id}. Error: {str(e)}"
        } 