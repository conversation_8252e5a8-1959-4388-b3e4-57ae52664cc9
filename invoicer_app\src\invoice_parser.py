"""
PDF text extraction module for the MacInvoicer application.

This module handles text extraction from PDF files using a two-tier approach:
1. Primary extraction using PyPDF2 for text-based PDFs
2. Fallback OCR extraction using Tesseract for scanned/image-based PDFs

The module automatically determines the best extraction method based on the
quality and quantity of text extracted by PyPDF2.

Key Features:
    - Dual-method text extraction (PyPDF2 + OCR)
    - Automatic fallback to OCR for poor text extraction
    - Support for encrypted PDFs (with empty password)
    - Configurable OCR tools (Tesseract, Poppler)
    - Comprehensive error handling and logging

Dependencies:
    - PyPDF2: For direct PDF text extraction
    - pytesseract: For OCR text extraction
    - PIL (Pillow): For image processing
    - pdf2image: For PDF to image conversion
    - poppler-utils: Required by pdf2image

Author: MacInvoicer Development Team
Version: 1.0.0
"""

import PyPDF2
import pytesseract
from PIL import Image
from pdf2image import convert_from_path
import os
import re
from config_loader import TESSERACT_CMD_PATH, POPPLER_PATH
from app_logger import app_logger

MIN_TEXT_LENGTH_FROM_PYPDF2 = 100 # If PyPDF2 extracts less than this, try OCR

if TESSERACT_CMD_PATH:
    pytesseract.tesseract_cmd = TESSERACT_CMD_PATH

def preprocess_ocr_text(text):
    """
    Preprocesses OCR extracted text to fix common OCR artifacts and improve AI parsing.
    
    This function addresses common OCR issues that can lead to incorrect data extraction:
    1. Fixes spacing issues around numbers and decimal points
    2. Identifies and properly formats total amount lines
    3. Separates incorrectly concatenated numbers
    4. Normalizes currency and total keywords
    
    Args:
        text (str): Raw OCR extracted text content.
        
    Returns:
        str: Cleaned and normalized text ready for AI processing.
        
    Note:
        Specifically addresses the issue where "23 727.64 167.36" gets misread
        as a single number 23727.64 instead of separate values.
    """
    if not text or not text.strip():
        return text
    
    app_logger.info("Applying OCR text preprocessing...")
    
    lines = text.split('\n')
    processed_lines = []
    
    for line in lines:
        original_line = line
        
        # Skip empty lines
        if not line.strip():
            processed_lines.append(line)
            continue
            
        # Check if this line likely contains total information
        is_total_line = any(keyword in line.lower() for keyword in [
            'total', 'amount', 'sum', 'due', 'balance', 'subtotal', 'net', 'gross'
        ]) or any(symbol in line for symbol in ['€', '$', '£', '¥'])
        
        # Look for patterns of numbers that might be incorrectly concatenated
        # Pattern: number(s) followed by space(s) then decimal number(s)
        # Example: "23 727.64 167.36" or "1 234.56"
        number_pattern = r'(\d{1,3})\s+(\d{2,3}\.\d{2})(\s+\d+\.\d{2})?'
        
        if is_total_line and re.search(number_pattern, line):
            # For total lines, add clear separation and labels
            line = re.sub(number_pattern, r'Qty: \1 | Total: \2\3', line)
            app_logger.debug(f"OCR preprocessing - Total line: '{original_line}' -> '{line}'")
        elif re.search(r'\d+\s+\d+\.\d{2}', line):
            # For other lines with similar patterns, just ensure proper spacing
            line = re.sub(r'(\d+)\s+(\d+\.\d{2})', r'\1 | \2', line)
            app_logger.debug(f"OCR preprocessing - Number separation: '{original_line}' -> '{line}'")
        
        # Standardize spacing around decimal numbers
        line = re.sub(r'(\d)\s+(\.\d{2})', r'\1\2', line)  # Fix "123 .45" -> "123.45"
        line = re.sub(r'(\d{1,3})\s+(\d{3}\.\d{2})', r'\1,\2', line)  # Fix "1 234.56" -> "1,234.56"
        
        # Ensure proper spacing around currency symbols
        line = re.sub(r'([€$£¥])(\d)', r'\1 \2', line)  # "€123.45" -> "€ 123.45"
        line = re.sub(r'(\d)([€$£¥])', r'\1 \2', line)  # "123.45€" -> "123.45 €"
        
        # Clean up excessive whitespace
        line = re.sub(r'\s+', ' ', line).strip()
        
        processed_lines.append(line)
    
    processed_text = '\n'.join(processed_lines)
    
    # Log a summary of changes made
    original_lines = len([l for l in text.split('\n') if l.strip()])
    processed_lines_count = len([l for l in processed_lines if l.strip()])
    
    if processed_text != text:
        app_logger.info(f"OCR preprocessing completed. Lines processed: {original_lines}")
        app_logger.debug("OCR preprocessing made formatting improvements to assist AI parsing")
    else:
        app_logger.debug("OCR preprocessing: No changes needed")
    
    return processed_text

def ocr_image(image_path_or_object):
    """
    Performs OCR (Optical Character Recognition) on a single image.
    
    This function extracts text from an image using Tesseract OCR. It can handle
    both file paths to images and PIL Image objects directly.
    
    Args:
        image_path_or_object (str or PIL.Image.Image): Either a file path to an image
                                                      or a PIL Image object.
                                                      
    Returns:
        str or None: Extracted text from the image, or None if OCR fails.
        
    Raises:
        TesseractNotFoundError: If Tesseract is not installed or not in PATH.
        Exception: For any other OCR processing errors.
        
    Note:
        Tesseract must be installed and either in the system PATH or specified
        via the TESSERACT_CMD_PATH environment variable.
    """
    try:
        text = pytesseract.image_to_string(image_path_or_object)
        return text
    except pytesseract.TesseractNotFoundError:
        app_logger.error("TesseractNotFoundError: Tesseract is not installed or not in your PATH.")
        app_logger.error("Please install Tesseract and/or set TESSERACT_CMD_PATH in your .env file.")
        return None
    except Exception as e:
        app_logger.error(f"Error during OCR: {e}", exc_info=True)
        return None

def extract_text_from_pdf(pdf_path):
    """
    Extracts text from a PDF file using a two-tier approach.
    
    This function first attempts to extract text directly from the PDF using PyPDF2.
    If the extracted text is insufficient (below MIN_TEXT_LENGTH_FROM_PYPDF2), it
    falls back to OCR by converting PDF pages to images and processing them with Tesseract.
    
    The two-tier approach ensures optimal performance for text-based PDFs while
    providing OCR fallback for scanned documents or PDFs with embedded images.
    
    Args:
        pdf_path (str): Absolute path to the PDF file to process.
        
    Returns:
        str or None: Extracted text from the PDF, or None if both methods fail
                    to extract any text.
                    
    Raises:
        FileNotFoundError: If the PDF file doesn't exist.
        Exception: Various PDF processing errors are caught and logged.
        
    Note:
        - Supports encrypted PDFs with empty passwords
        - Requires Poppler for PDF to image conversion (OCR fallback)
        - Requires Tesseract for OCR functionality
        - Falls back gracefully if OCR tools are not available
    """
    extracted_text = ""
    # Attempt 1: PyPDF2
    try:
        with open(pdf_path, 'rb') as pdf_file_obj:
            pdf_reader = PyPDF2.PdfReader(pdf_file_obj)
            if pdf_reader.is_encrypted:
                try:
                    # Try with empty password
                    # PyPDF2 < 3.0.0: pdf_reader.decrypt('')
                    # PyPDF2 >= 3.0.0: Returns 0 if successful, 1 if password incorrect, 2 if not supported
                    # For simplicity, we just try. If it fails, it might raise an exception or reading will yield no text.
                    if hasattr(pdf_reader, 'decrypt') and callable(getattr(pdf_reader, 'decrypt')):
                         # Check return value if needed, or just let it proceed
                        pass # pdf_reader.decrypt('') # PyPDF2 might handle this internally now for some cases or error on open
                except Exception as ex:
                    app_logger.warning(f"Could not decrypt PDF {pdf_path}: {ex}. OCR will likely fail too if it's protected.")
            
            for page_num in range(len(pdf_reader.pages)):
                page_obj = pdf_reader.pages[page_num]
                extracted_text += page_obj.extract_text() or "" # Ensure it concatenates empty string if None
        
        if len(extracted_text.strip()) >= MIN_TEXT_LENGTH_FROM_PYPDF2:
            app_logger.info(f"Successfully extracted text from {pdf_path} using PyPDF2.")
            return preprocess_ocr_text(extracted_text.strip())
        else:
            app_logger.info(f"PyPDF2 extracted very little text ({len(extracted_text.strip())} chars) from {pdf_path}. Attempting OCR as fallback.")
    except Exception as e:
        app_logger.error(f"Error reading PDF {pdf_path} with PyPDF2: {e}. Attempting OCR as fallback.", exc_info=True)

    # Attempt 2: OCR with Tesseract if PyPDF2 failed or text was too short
    ocr_texts = []
    try:
        app_logger.info(f"Performing OCR on {pdf_path}...")
        images = convert_from_path(pdf_path, poppler_path=POPPLER_PATH)
        if not images:
            app_logger.warning(f"pdf2image could not convert {pdf_path} to images. Is Poppler installed and in PATH or POPPLER_PATH set in .env?")
            return extracted_text.strip() if extracted_text.strip() else None # Or return the minimal text from PyPDF2 if any
            
        for i, image in enumerate(images):
            # Optionally save images for debugging:
            # temp_image_path = f"temp_page_{i}.png"
            # image.save(temp_image_path, "PNG")
            # page_text = ocr_image(temp_image_path)
            # os.remove(temp_image_path)
            page_text = ocr_image(image) # Process image object directly
            if page_text:
                ocr_texts.append(page_text)
        
        full_ocr_text = "\n".join(ocr_texts)
        if full_ocr_text.strip():
            app_logger.info(f"Successfully extracted text from {pdf_path} using OCR.")
            return preprocess_ocr_text(full_ocr_text.strip())
        else:
            app_logger.warning(f"OCR attempt on {pdf_path} yielded no text.")
            return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
            
    except pytesseract.TesseractNotFoundError:
        app_logger.error("TesseractNotFoundError during PDF parse: Tesseract is not installed or not in your PATH/TESSERACT_CMD_PATH.")
        app_logger.warning("OCR functionality will be disabled for this file.")
        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
    except FileNotFoundError as fnfe:
        # This can happen if Poppler is not found by pdf2image
        app_logger.error(f"FileNotFoundError during OCR prep for {pdf_path}: {fnfe}")
        app_logger.warning("This might be due to Poppler not being installed or not in PATH/POPPLER_PATH.")
        app_logger.warning("OCR functionality will be disabled for this file.")
        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None
    except Exception as e:
        app_logger.error(f"Error during OCR processing for {pdf_path}: {e}", exc_info=True)
        return preprocess_ocr_text(extracted_text.strip()) if extracted_text.strip() else None

    # If both methods yield nothing substantial
    return None if not extracted_text.strip() else preprocess_ocr_text(extracted_text.strip()) 