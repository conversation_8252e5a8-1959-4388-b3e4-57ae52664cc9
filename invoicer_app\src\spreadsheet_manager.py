"""
Spreadsheet management module for the MacInvoicer application.

This module handles all Excel spreadsheet operations including creating monthly
spreadsheets, appending invoice data, and maintaining running totals. It provides
automated organization by creating separate spreadsheets for each month.

Key Features:
    - Monthly spreadsheet organization (YYYY-MM_invoices.xlsx format)
    - Automatic directory creation for output files
    - Running total calculations for subtotal, VAT, and total amounts
    - Structured data columns with consistent formatting
    - Duplicate detection and handling
    - Business unit tracking
    - Error handling for file operations
    - Support for existing spreadsheet updates

Data Structure:
    Each spreadsheet contains columns for processing datetime, invoice details,
    extracted amounts (subtotal, VAT, total), currency information, business units,
    AI confidence scores, running totals, and duplicate detection flags.

Author: MacInvoicer Development Team
Version: 2.0.0
"""

import pandas as pd
import os
import datetime
from config_loader import OUTPUT_PATH
from app_logger import app_logger

# OUTPUT_DIR_NAME is used internally if OUTPUT_PATH is not directly used from config
# However, it's better to rely on OUTPUT_PATH from config_loader for consistency.
# BASE_OUTPUT_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "output_spreadsheets")
# Using OUTPUT_PATH from config_loader directly:
BASE_OUTPUT_PATH = OUTPUT_PATH

def get_monthly_spreadsheet_path():
    """
    Determines the file path for the current month's invoice spreadsheet.
    
    This function generates a standardized file path for the current month's
    invoice spreadsheet using the format YYYY-MM_invoices.xlsx. It also ensures
    the output directory exists, creating it if necessary.
    
    Returns:
        str or None: Full path to the monthly spreadsheet file, or None if
                    directory creation fails.
                    
    Raises:
        Exception: Directory creation errors are caught and logged.
        
    Note:
        The spreadsheet path format is: {OUTPUT_PATH}/{YYYY-MM}_invoices.xlsx
        where YYYY-MM represents the current year and month.
    """
    if not os.path.exists(BASE_OUTPUT_PATH):
        try:
            os.makedirs(BASE_OUTPUT_PATH)
            app_logger.info(f"Created output spreadsheet directory: {BASE_OUTPUT_PATH}")
        except Exception as e:
            app_logger.error(f"Failed to create output spreadsheet directory {BASE_OUTPUT_PATH}: {e}", exc_info=True)
            # Potentially raise or handle if critical
            return None # Indicate failure

    current_month_str = datetime.date.today().strftime("%Y-%m")
    return os.path.join(BASE_OUTPUT_PATH, f"{current_month_str}_invoices.xlsx")

def check_for_duplicates(data_dict, invoice_filename):
    """
    Checks for duplicate invoices in the current month's spreadsheet.
    
    This function performs multiple levels of duplicate detection:
    1. Exact invoice ID match from the same vendor
    2. Same vendor + date + amount combination
    3. Similar vendor names with same date and amount (fuzzy matching)
    
    Args:
        data_dict (dict): Dictionary containing extracted invoice data.
        invoice_filename (str): Name of the invoice file being processed.
        
    Returns:
        tuple: (is_duplicate: bool, duplicate_reason: str or None)
               is_duplicate indicates if a duplicate was found
               duplicate_reason provides explanation if duplicate found
               
    Note:
        Only checks against the current month's spreadsheet. Cross-month
        duplicate detection would require additional implementation.
    """
    spreadsheet_path = get_monthly_spreadsheet_path()
    if not spreadsheet_path or not os.path.exists(spreadsheet_path):
        # No existing spreadsheet, so no duplicates possible
        return False, None
    
    try:
        existing_df = pd.read_excel(spreadsheet_path)
        if existing_df.empty:
            return False, None
            
        invoice_id = data_dict.get("invoice_id")
        vendor_name = data_dict.get("vendor_name")
        invoice_date = data_dict.get("invoice_date")
        total_amount = data_dict.get("total_amount_eur")
        
        # Check 1: Exact invoice ID match (if invoice_id is not null/error)
        if invoice_id and not str(invoice_id).startswith("ERROR"):
            id_matches = existing_df[existing_df['Invoice_ID'] == invoice_id]
            if not id_matches.empty:
                return True, f"Duplicate Invoice ID: {invoice_id}"
        
        # Check 2: Same vendor + date + amount combination
        if vendor_name and invoice_date and total_amount is not None:
            combo_matches = existing_df[
                (existing_df['Vendor_Name'] == vendor_name) &
                (existing_df['Invoice_Date'] == invoice_date) &
                (existing_df['Total_Amount_EUR'] == total_amount)
            ]
            if not combo_matches.empty:
                return True, f"Duplicate vendor+date+amount: {vendor_name}, {invoice_date}, €{total_amount}"
        
        # Check 3: Same filename (exact file already processed)
        if invoice_filename:
            filename_matches = existing_df[existing_df['Invoice_Filename'] == invoice_filename]
            if not filename_matches.empty:
                return True, f"Duplicate filename: {invoice_filename}"
        
        # Check 4: Fuzzy vendor name matching with same date and amount
        if vendor_name and invoice_date and total_amount is not None:
            # Simple fuzzy matching - check if vendor names are very similar
            for _, row in existing_df.iterrows():
                existing_vendor = str(row.get('Vendor_Name', '')).lower().strip()
                current_vendor = str(vendor_name).lower().strip()
                
                # Check if one vendor name is contained in the other (simple fuzzy match)
                if (len(existing_vendor) > 3 and len(current_vendor) > 3 and
                    (existing_vendor in current_vendor or current_vendor in existing_vendor) and
                    row.get('Invoice_Date') == invoice_date and
                    abs(float(row.get('Total_Amount_EUR', 0)) - float(total_amount)) < 0.01):
                    return True, f"Similar vendor+date+amount: {existing_vendor} ≈ {current_vendor}, {invoice_date}, €{total_amount}"
        
        return False, None
        
    except Exception as e:
        app_logger.error(f"Error checking for duplicates: {e}", exc_info=True)
        # If we can't check for duplicates, assume no duplicate to avoid blocking processing
        return False, f"Duplicate check failed: {str(e)}"

def calculate_subtotal(total_amount, vat_amount):
    """
    Calculates the subtotal amount (excluding VAT) from total and VAT amounts.
    
    Args:
        total_amount (float or None): Total amount including VAT.
        vat_amount (float or None): VAT amount.
        
    Returns:
        float or None: Subtotal amount (total - VAT), or None if calculation not possible.
    """
    if total_amount is not None and vat_amount is not None:
        return total_amount - vat_amount
    elif total_amount is not None and vat_amount is None:
        # If we have total but no VAT, we can't calculate subtotal accurately
        # Return None to indicate unknown subtotal
        return None
    else:
        return None

def append_to_spreadsheet(data_dict, invoice_filename):
    """
    Appends invoice data to the current month's spreadsheet with enhanced features.
    
    This function takes extracted invoice data and appends it as a new row to the
    appropriate monthly spreadsheet. It handles both new spreadsheet creation and
    updates to existing spreadsheets. Running totals are automatically calculated
    and maintained across all entries. Includes duplicate detection and business
    unit tracking.
    
    Args:
        data_dict (dict): Dictionary containing extracted invoice data with keys:
                         - invoice_id: Invoice reference number
                         - vendor_name: Name of the issuing company
                         - invoice_date: Date of the invoice
                         - due_date: Payment due date
                         - total_amount_eur: Total amount in EUR (including VAT)
                         - vat_amount_eur: VAT amount in EUR
                         - business_unit: Business unit or department
                         - original_currency: Original currency code
                         - original_total_amount: Amount in original currency
                         - ai_confidence_score: AI extraction confidence (0-1)
                         - processing_notes: Processing information
        invoice_filename (str): Name of the processed invoice file.
        
    Returns:
        None: Function performs file operations but returns nothing.
        
    Raises:
        Exception: File operation errors are caught and logged.
        
    Note:
        - Creates new spreadsheet if one doesn't exist for the current month
        - Recalculates running totals for the entire spreadsheet on each update
        - Uses current datetime for processing timestamp
        - Maintains data consistency across multiple invoice additions
        - Includes duplicate detection and flagging
        - Calculates subtotal automatically from total and VAT amounts
    """
    spreadsheet_path = get_monthly_spreadsheet_path()
    if not spreadsheet_path:
        app_logger.error("Could not determine spreadsheet path. Aborting append operation.")
        return
    
    # Check for duplicates
    is_duplicate, duplicate_reason = check_for_duplicates(data_dict, invoice_filename)
    if is_duplicate:
        app_logger.warning(f"Duplicate detected for {invoice_filename}: {duplicate_reason}")
    
    processing_datetime = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Get amounts from data_dict, preferring AI-extracted if available
    total_amount = data_dict.get("total_amount_eur")
    vat_amount = data_dict.get("vat_amount_eur")
    subtotal_amount_ai = data_dict.get("subtotal_eur") # From AI

    # Use AI subtotal if available and valid, otherwise calculate
    if subtotal_amount_ai is not None and isinstance(subtotal_amount_ai, (int, float)):
        subtotal_amount = subtotal_amount_ai
        app_logger.debug(f"Using AI-provided subtotal for {invoice_filename}: {subtotal_amount}")
    else:
        subtotal_amount = calculate_subtotal(total_amount, vat_amount)
        app_logger.debug(f"Calculated subtotal for {invoice_filename}: {subtotal_amount} (AI subtotal was: {subtotal_amount_ai})")
    
    new_data_df = pd.DataFrame([{
        # Processing Information
        "Processing_DateTime": processing_datetime,
        "Invoice_Filename": invoice_filename,
        
        # Invoice Details
        "Invoice_ID": data_dict.get("invoice_id", "N/A"),
        "Vendor_Name": data_dict.get("vendor_name", "N/A"),
        "Invoice_Date": data_dict.get("invoice_date", "N/A"),
        "Due_Date": data_dict.get("due_date", "N/A"),
        "Business_Unit": data_dict.get("business_unit", "N/A"),
        
        # Financial Amounts (EUR) - Grouped Together
        "Subtotal_EUR": subtotal_amount if subtotal_amount is not None else 0.0,
        "VAT_Amount_EUR": vat_amount if vat_amount is not None else 0.0,
        "Total_Amount_EUR": total_amount if total_amount is not None else 0.0,
        
        # Running Totals - Grouped Together
        "Running_Subtotal_EUR": 0.0,  # Will be calculated later
        "Running_VAT_EUR": 0.0,       # Will be calculated later
        "Running_Total_EUR": 0.0,     # Will be calculated later
        
        # Original Currency Information
        "Original_Currency": data_dict.get("original_currency", "N/A"),
        "Original_Subtotal_Amount": data_dict.get("original_subtotal"), # New
        "Original_VAT_Amount": data_dict.get("original_vat_amount"),   # New
        "Original_Total_Amount": data_dict.get("original_total_amount", 0.0),
        
        # AI and Processing Specifics
        "AI_Confidence_Score": data_dict.get("ai_confidence_score", 0.0),
        "Processing_Notes": data_dict.get("processing_notes", ""),
        "Processing_Notes_Ref": data_dict.get("processing_notes_ref", ""), # New
        "Is_Duplicate_AI": data_dict.get("is_duplicate", False), # New - Flag from AI
        "Is_Duplicate_System": is_duplicate, # Existing system duplicate check
        "Duplicate_Reason": duplicate_reason if is_duplicate else None,

        # Include API tracking fields from AI handler if needed for audit in Excel
        "API_Session_ID": data_dict.get("responses_api_session_id", ""),
        "API_Response_ID": data_dict.get("responses_api_response_id", ""),
        "API_Model_Used": data_dict.get("responses_api_model_used", "")
    }])

    # Define column order for consistency
    column_order = [
        "Processing_DateTime", "Invoice_Filename", "Invoice_ID", "Vendor_Name", 
        "Invoice_Date", "Due_Date", "Business_Unit",
        "Subtotal_EUR", "VAT_Amount_EUR", "Total_Amount_EUR",
        "Running_Subtotal_EUR", "Running_VAT_EUR", "Running_Total_EUR",
        "Original_Currency", "Original_Subtotal_Amount", "Original_VAT_Amount", "Original_Total_Amount",
        "AI_Confidence_Score", "Processing_Notes", "Processing_Notes_Ref",
        "Is_Duplicate_AI", "Is_Duplicate_System", "Duplicate_Reason",
        "API_Session_ID", "API_Response_ID", "API_Model_Used"
    ]

    try:
        if os.path.exists(spreadsheet_path):
            existing_df = pd.read_excel(spreadsheet_path)
            updated_df = pd.concat([existing_df, new_data_df], ignore_index=True)
        else:
            updated_df = new_data_df
        
        # Calculate running totals for the entire sheet to ensure accuracy
        # Handle cases where amounts might be None or NaN
        updated_df['Subtotal_EUR'] = pd.to_numeric(updated_df['Subtotal_EUR'], errors='coerce').fillna(0)
        updated_df['VAT_Amount_EUR'] = pd.to_numeric(updated_df['VAT_Amount_EUR'], errors='coerce').fillna(0)
        updated_df['Total_Amount_EUR'] = pd.to_numeric(updated_df['Total_Amount_EUR'], errors='coerce').fillna(0)
        
        # Calculate running totals
        updated_df['Running_Subtotal_EUR'] = updated_df['Subtotal_EUR'].cumsum()
        updated_df['Running_VAT_EUR'] = updated_df['VAT_Amount_EUR'].cumsum()
        updated_df['Running_Total_EUR'] = updated_df['Total_Amount_EUR'].cumsum()
        
        # Ensure column order is maintained (reorder columns to match our desired structure)
        existing_columns = list(updated_df.columns)
        ordered_columns = [col for col in column_order if col in existing_columns]
        extra_columns = [col for col in existing_columns if col not in column_order]
        final_column_order = ordered_columns + extra_columns
        
        updated_df = updated_df[final_column_order]

        updated_df.to_excel(spreadsheet_path, index=False)
        
        if is_duplicate:
            app_logger.warning(f"Data for {invoice_filename} appended to {spreadsheet_path} with DUPLICATE flag: {duplicate_reason}")
        else:
            app_logger.info(f"Data for {invoice_filename} appended to {spreadsheet_path}")

    except Exception as e:
        app_logger.error(f"Error updating spreadsheet {spreadsheet_path}: {e}", exc_info=True) 