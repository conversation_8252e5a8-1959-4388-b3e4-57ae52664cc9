import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../src')))
import pytest
from unittest import mock
import invoice_parser

def test_ocr_image_with_valid_image():
    """
    Test ocr_image with a valid image path (mocked).
        """
    print("Test: ocr_image should return text for a valid image (mocked).")
    with mock.patch('invoice_parser.pytesseract.image_to_string', return_value='Invoice Text'):
        result = invoice_parser.ocr_image('dummy_path.png')
        assert result == 'Invoice Text'
        print(f"OCR result: {result}")

def test_extract_text_from_pdf_with_valid_pdf():
    """
    Test extract_text_from_pdf with a valid PDF path (mocked).
    """
    print("Test: extract_text_from_pdf should return text for a valid PDF (mocked).")
    with mock.patch('invoice_parser.PyPDF2.PdfReader') as mock_reader:
        mock_reader.return_value.pages = [mock.Mock(extract_text=mock.Mock(return_value='PDF Text'))]
        result = invoice_parser.extract_text_from_pdf('dummy.pdf')
        assert result == 'PDF Text'
        print(f"PDF extraction result: {result}") 