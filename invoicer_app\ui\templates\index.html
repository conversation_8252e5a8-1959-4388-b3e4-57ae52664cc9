{% extends "base.html" %}

{% block title %}Invoice Review Dashboard{% endblock %}

{% block content %}
<h1>Invoice Review Dashboard</h1>

<!-- DEBUG: Show invoice count -->
<div style="background: #ffeb3b; padding: 10px; margin: 10px 0; border: 1px solid #ccc;">
    <strong>DEBUG INFO:</strong><br>
    Total invoices received: {{ invoices|length }}<br>
    {% if invoices %}
        First invoice: {{ invoices[0].filename }} ({{ invoices[0].status }})<br>
    {% else %}
        No invoices found!<br>
    {% endif %}
</div>

<div class="stats">
    <div class="stat-card">
        <h3>Pending</h3>
        <p>{{ invoices|selectattr('status', 'equalto', 'pending')|list|length }}</p>
    </div>
    <div class="stat-card">
        <h3>Approved</h3>
        <p>{{ invoices|selectattr('status', 'equalto', 'approved')|list|length }}</p>
    </div>
    <div class="stat-card">
        <h3>Rejected</h3>
        <p>{{ invoices|selectattr('status', 'equalto', 'rejected')|list|length }}</p>
    </div>
</div>

<table class="invoice-table">
    <thead>
        <tr>
            <th>Filename</th>
            <th>Status</th>
            <th>Created</th>
            <th>Action</th>
        </tr>
    </thead>
    <tbody>
        {% for invoice in invoices %}
        <tr class="status-{{ invoice.status }}">
            <td>{{ invoice.filename }}</td>
            <td>{{ invoice.status.title() }}</td>
            <td>{{ invoice.created_at[:16] if invoice.created_at else 'N/A' }}</td>
            <td>
                <a href="{{ url_for('review_invoice', invoice_id=invoice.id) }}" class="btn">Review</a>
            </td>
        </tr>
        {% else %}
        <tr>
            <td colspan="4" style="text-align: center; color: red; font-weight: bold;">
                NO INVOICES FOUND IN TEMPLATE - Check console for errors
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<div class="export-section">
    <h3>Export Data</h3>
    <a href="/export/approved" class="btn btn-success">Export Approved</a>
    <a href="/export/rejected" class="btn btn-danger">Export Rejected</a>
</div>
{% endblock %} 