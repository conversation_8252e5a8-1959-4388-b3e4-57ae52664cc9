{% extends "base.html" %}

{% block title %}Upload Invoices{% endblock %}

{% block extra_styles %}
.upload-form {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 8px;
    margin-top: 20px;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    font-weight: 500;
    display: block;
    margin-bottom: 8px;
}
.form-control-file {
    display: block;
    width: 100%;
    padding: 10px;
    font-size: 1rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
}
{% endblock %}

{% block navigation %}
<div class="navigation">
    <a href="{{ url_for('index') }}">← Back to Review Dashboard</a>
</div>
{% endblock %}

{% block content %}
<h1>Upload PDF Invoices</h1>
<p>Select one or more PDF files to upload. They will be placed in the monitored folder for processing.</p>

<div class="upload-form">
    <form action="{{ url_for('upload_files') }}" method="post" enctype="multipart/form-data">
        <div class="form-group">
            <label for="files">Choose PDF Files</label>
            <input type="file" name="files[]" id="files" class="form-control-file" multiple required accept=".pdf">
        </div>
        <button type="submit" class="btn btn-primary">Upload Files</button>
    </form>
</div>
{% endblock %} 